import siteConfig from '../../content/site.json';
import architectureIndex from '../../content/architecture/index.json';
import architectureFoundations from '../../content/architecture/architecture-foundations.json';
import devopsIndex from '../../content/devops/index.json';
import platformMaturity from '../../content/devops/platform-maturity.json';
import agileIndex from '../../content/agile/index.json';
import teamPractices from '../../content/agile/team-practices.json';
import qualityIndex from '../../content/quality/index.json';
import testingStrategy from '../../content/quality/testing-strategy.json';
import type {ThemeIndex, MaturityModel, SiteConfig, SearchIndexItem} from '@/src/types';

// Static content registry
const contentRegistry = {
  'site.json': siteConfig,
  'architecture/index.json': architectureIndex,
  'architecture/architecture-foundations.json': architectureFoundations,
  'devops/index.json': devopsIndex,
  'devops/platform-maturity.json': platformMaturity,
  'agile/index.json': agileIndex,
  'agile/team-practices.json': teamPractices,
  'quality/index.json': qualityIndex,
  'quality/testing-strategy.json': testingStrategy,
};

export function readJson<T>(filePath: string): T {
  const content = contentRegistry[filePath as keyof typeof contentRegistry];
  if (!content) {
    throw new Error(`Content not found: ${filePath}`);
  }
  return content as T;
}

export function listThemes(): string[] {
  return ['architecture', 'devops', 'agile', 'quality'];
}

export function getThemeIndex(theme: string): ThemeIndex {
  return readJson<ThemeIndex>(`${theme}/index.json`);
}

export function getModel(theme: string, modelId: string): MaturityModel {
  return readJson<MaturityModel>(`${theme}/${modelId}.json`);
}

export function getSiteConfig(): SiteConfig {
  return readJson<SiteConfig>('site.json');
}

export function getAllThemes() {
  const themes = listThemes();
  return themes
    .map((theme) => {
      try {
        return getThemeIndex(theme);
      } catch {
        return null;
      }
    })
    .filter((t) => !!t);
}

// Build search index from all content
export function buildSearchIndex(): SearchIndexItem[] {
  const themes = getAllThemes();
  const index: SearchIndexItem[] = [];

  themes.forEach((theme) => {
    if (!theme) return;

    theme.models.forEach((modelRef) => {
      try {
        const model = getModel(theme.theme, modelRef.modelId);

        // Add model to index
        index.push({
          theme: theme.theme,
          modelId: model.modelId,
          modelTitle: model.title,
          text: `${model.title} ${model.shortDescription || ''} ${model.overviewMd || ''}`,
          url: `/t/${theme.theme}/${model.modelId}`,
        });

        // Add dimensions to index
        model.dimensions.forEach((dimension) => {
          const cellText = Object.values(dimension.cells).join(' ');
          index.push({
            theme: theme.theme,
            modelId: model.modelId,
            modelTitle: model.title,
            dimensionId: dimension.id,
            dimensionLabel: dimension.label,
            text: `${dimension.label} ${cellText} ${dimension.assessmentMd || ''} ${dimension.nextStepsMd || ''}`,
            url: `/t/${theme.theme}/${model.modelId}/d/${dimension.id}`,
          });
        });
      } catch (error) {
        console.warn(`Failed to index model ${modelRef.modelId}:`, error);
      }
    });
  });

  return index;
}

@media print {
  /* Hide navigation and non-essential elements */
  aside,
  header,
  footer,
  .no-print {
    display: none !important;
  }

  /* Adjust layout for print */
  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  /* Ensure content uses full width */
  main {
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Table styling for print */
  table {
    border-collapse: collapse;
    width: 100%;
    font-size: 10pt;
  }

  th,
  td {
    border: 1px solid #000;
    padding: 4pt;
    text-align: left;
    vertical-align: top;
  }

  th {
    background-color: #f0f0f0;
    font-weight: bold;
  }

  /* Page breaks */
  .page-break {
    page-break-before: always;
  }

  /* Avoid breaking inside elements */
  .avoid-break {
    page-break-inside: avoid;
  }

  /* Headers */
  h1,
  h2,
  h3 {
    page-break-after: avoid;
  }

  /* Links */
  a {
    text-decoration: none;
    color: inherit;
  }

  a[href]:after {
    content: " (" attr(href) ")";
    font-size: 9pt;
    color: #666;
  }
}

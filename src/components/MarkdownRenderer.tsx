import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeSanitize from 'rehype-sanitize';

type MarkdownRendererProps = {
  content?: string;
};

export function MarkdownRenderer({content}: MarkdownRendererProps) {
  if (!content) return null;

  return (
    <ReactMarkdown
      remarkPlugins={[remarkGfm]}
      rehypePlugins={[rehypeSanitize]}
      components={{
        a: ({href, children, ...props}) => (
          <a
            href={href}
            {...props}
            className="text-primary underline hover:no-underline"
            target={href?.startsWith('http') ? '_blank' : undefined}
            rel={href?.startsWith('http') ? 'noopener noreferrer' : undefined}
          >
            {children}
          </a>
        ),
        h1: ({children, ...props}) => (
          <h1 {...props} className="text-2xl font-semibold mb-4 text-balance">
            {children}
          </h1>
        ),
        h2: ({children, ...props}) => (
          <h2 {...props} className="text-xl font-semibold mb-3 text-balance">
            {children}
          </h2>
        ),
        h3: ({children, ...props}) => (
          <h3 {...props} className="text-lg font-semibold mb-2 text-balance">
            {children}
          </h3>
        ),
        p: ({children, ...props}) => (
          <p {...props} className="mb-4 text-pretty leading-relaxed">
            {children}
          </p>
        ),
        ul: ({children, ...props}) => (
          <ul {...props} className="list-disc pl-6 mb-4 space-y-1">
            {children}
          </ul>
        ),
        ol: ({children, ...props}) => (
          <ol {...props} className="list-decimal pl-6 mb-4 space-y-1">
            {children}
          </ol>
        ),
        li: ({children, ...props}) => (
          <li {...props} className="text-pretty">
            {children}
          </li>
        ),
        blockquote: ({children, ...props}) => (
          <blockquote {...props} className="border-l-4 border-border pl-4 italic mb-4">
            {children}
          </blockquote>
        ),
        code: ({children, ...props}) => (
          <code {...props} className="bg-muted px-1 py-0.5 rounded text-sm font-mono">
            {children}
          </code>
        ),
        pre: ({children, ...props}) => (
          <pre {...props} className="bg-muted p-4 rounded overflow-x-auto mb-4">
            {children}
          </pre>
        ),
      }}
    >
      {content}
    </ReactMarkdown>
  );
}

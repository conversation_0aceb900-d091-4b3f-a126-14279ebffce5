// Shared type definitions for the software maturity models application

export type Level = {
  id: string;
  label: string;
  color?: string;
};

export type Dimension = {
  id: string;
  label: string;
  cells: Record<string, string>;
  assessmentMd?: string;
  nextStepsMd?: string;
};

export type MaturityModel = {
  schemaVersion: string;
  theme: string;
  modelId: string;
  title: string;
  shortDescription?: string;
  overviewMd?: string;
  levels?: Level[];
  dimensions: Dimension[];
  howToAssessMd?: string;
  improvementPathsMd?: string;
  references?: {label: string; url: string}[];
  tags?: string[];
  version?: string;
  lastUpdated?: string;
};

export type ThemeIndex = {
  theme: string;
  title: string;
  descriptionMd?: string;
  models: {modelId: string; title: string}[];
};

export type SiteConfig = {
  siteTitle: string;
  tagline: string;
  defaultLevels: Level[];
  footerMd?: string;
  links?: {label: string; url: string}[];
  overviewMd?: string;
};

export type SearchIndexItem = {
  theme: string;
  modelId: string;
  modelTitle: string;
  dimensionId?: string;
  dimensionLabel?: string;
  text: string;
  url: string;
};

{"schemaVersion": "1.0", "theme": "devops", "modelId": "platform-maturity", "title": "Platform Engineering Maturity Model", "shortDescription": "Assess and improve your platform engineering capabilities from basic infrastructure to advanced developer experience.", "overviewMd": "## Overview\n\nThis model helps organizations build world-class platform engineering capabilities that enable developer productivity and operational excellence.\n\n**Key Focus Areas:**\n- Infrastructure as Code\n- Developer Self-Service\n- Observability and Monitoring\n- Security Integration", "dimensions": [{"id": "infrastructure-as-code", "label": "Infrastructure as Code", "cells": {"0": "Manual infrastructure provisioning and configuration.", "1": "Some infrastructure automation scripts exist but are not standardized.", "2": "Consistent IaC practices with version control and basic validation.", "3": "Comprehensive IaC with automated testing, compliance checks, and standardized modules.", "4": "Advanced IaC with policy as code, cost optimization, and automated drift detection.", "5": "Self-healing infrastructure with predictive scaling and autonomous optimization."}}, {"id": "developer-self-service", "label": "Developer Self-Service", "cells": {"0": "Developers must request all infrastructure and deployments through tickets.", "1": "Basic self-service capabilities exist but require significant manual intervention.", "2": "Developers can provision common resources through standardized interfaces.", "3": "Comprehensive self-service platform with templates, guardrails, and automated provisioning.", "4": "Advanced platform with intelligent recommendations, cost visibility, and automated optimization.", "5": "AI-powered platform that anticipates developer needs and provides proactive assistance."}}], "howToAssessMd": "### Assessment Guidelines\n\nEvaluate your current platform capabilities across each dimension.", "improvementPathsMd": "### Improvement Roadmap\n\nFocus on building foundational capabilities before advancing to higher levels.", "references": [{"label": "Platform Engineering Guide", "url": "https://platformengineering.org/"}], "tags": ["devops", "platform", "infrastructure"], "version": "2025.01.1", "lastUpdated": "2025-01-29"}
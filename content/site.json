{"siteTitle": "Software Maturity Models", "tagline": "Clarity on where you are and how to improve", "defaultLevels": [{"id": "0", "label": "Absent", "color": "#ef4444"}, {"id": "1", "label": "Initial", "color": "#f97316"}, {"id": "2", "label": "Managed", "color": "#eab308"}, {"id": "3", "label": "Defined", "color": "#22c55e"}, {"id": "4", "label": "Quantitatively Managed", "color": "#3b82f6"}, {"id": "5", "label": "Optimizing", "color": "#8b5cf6"}], "footerMd": "© 2025 Software Maturity Models. Open source under MIT license.", "links": [{"label": "GitHub", "url": "https://github.com/example/maturity-models"}, {"label": "Methodology", "url": "/methodology"}, {"label": "Glossary", "url": "/glossary"}, {"label": "Changelog", "url": "/changelog"}], "overviewMd": "## Welcome to Software Maturity Models\n\nThis site provides comprehensive maturity models across multiple domains to help organizations assess their current capabilities and plan improvements.\n\n### How to Use This Site\n\n1. **Browse by Theme** - Use the sidebar to explore different domains like Architecture, DevOps, or Security\n2. **Assess Your Organization** - Review each dimension and identify your current maturity level\n3. **Plan Improvements** - Use the guidance provided to advance to the next level\n\n### Maturity Levels Explained\n\nEach model uses a 6-level scale from 0 (Absent) to 5 (Optimizing), based on industry-standard maturity frameworks.", "heroMd": "## Clear, comparable models for engineering, product, and delivery\n\nSee your maturity. Level up your delivery.", "howItWorksMd": "### Three simple steps to assess and improve your team's maturity\n\n1. **Pick a Theme** - Choose from Architecture, Agile Delivery, Code Quality, or DevOps themes\n2. **Assess Your Level** - Evaluate your current practices against standardized maturity levels\n3. **Plan Next Steps** - Get clear guidance on how to advance to the next maturity level", "whoItsForMd": "### Designed for teams at every level of the organization\n\n**Executives** - Align strategy and funding with maturity. Make informed decisions about technology investments.\n\n**Engineering Leaders** - Benchmark practices and plan improvements. Build roadmaps based on proven frameworks.\n\n**ICs & Product Managers** - Make the case with clear, evidence-based levels. Communicate needs effectively to leadership.", "whyMaturityMattersMd": "### Higher maturity leads to measurable business outcomes\n\n- **3x Predictability** - More reliable delivery timelines\n- **50% Quality** - Fewer production incidents\n- **2x Velocity** - Faster feature delivery\n- **75% Risk** - Reduction in security issues"}
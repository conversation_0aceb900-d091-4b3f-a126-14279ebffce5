{"schemaVersion": "1.0", "theme": "agile", "modelId": "team-practices", "title": "Team Practices & Delivery", "shortDescription": "Assess your team's agile practices, sprint management, and delivery capabilities.", "overviewMd": "This model evaluates team practices across key dimensions of agile delivery, from basic sprint mechanics to advanced continuous improvement practices.", "dimensions": [{"id": "sprint-management", "label": "Sprint Management", "cells": {"0": "No structured sprints or planning", "1": "Basic sprint structure with irregular planning", "2": "Consistent sprint planning and reviews", "3": "Well-defined sprint processes with metrics", "4": "Data-driven sprint optimization", "5": "Continuous sprint process improvement"}, "assessmentMd": "Evaluate how your team plans, executes, and reviews sprint cycles.", "nextStepsMd": "Focus on establishing consistent sprint ceremonies and tracking velocity metrics."}, {"id": "team-collaboration", "label": "Team Collaboration", "cells": {"0": "Siloed work with minimal communication", "1": "Basic daily standups", "2": "Regular team ceremonies and communication", "3": "Strong collaboration practices and knowledge sharing", "4": "Cross-functional collaboration with other teams", "5": "Optimized collaboration with continuous feedback loops"}, "assessmentMd": "Assess how effectively your team collaborates and communicates.", "nextStepsMd": "Implement regular retrospectives and improve knowledge sharing practices."}, {"id": "delivery-practices", "label": "Delivery Practices", "cells": {"0": "Ad-hoc delivery with no process", "1": "Basic delivery process", "2": "Consistent delivery with some automation", "3": "Reliable delivery with quality gates", "4": "Optimized delivery with metrics tracking", "5": "Continuous delivery with full automation"}, "assessmentMd": "Review your team's approach to delivering working software.", "nextStepsMd": "Focus on automating delivery processes and implementing quality gates."}], "howToAssessMd": "## How to Assess\n\n1. Review your current sprint practices and team ceremonies\n2. Evaluate collaboration patterns and communication effectiveness\n3. Assess delivery frequency and quality\n4. Identify gaps in current practices", "improvementPathsMd": "## Improvement Paths\n\n- **Level 0→1**: Establish basic sprint structure and daily standups\n- **Level 1→2**: Implement consistent sprint ceremonies and planning\n- **Level 2→3**: Add metrics tracking and quality processes\n- **Level 3→4**: Optimize based on data and cross-team collaboration\n- **Level 4→5**: Achieve continuous improvement and full automation", "references": [{"label": "Scrum Guide", "url": "https://scrumguides.org/"}, {"label": "<PERSON><PERSON><PERSON>", "url": "https://agilemanifesto.org/"}], "tags": ["agile", "scrum", "delivery", "team-practices"], "version": "1.0", "lastUpdated": "2025-01-01"}
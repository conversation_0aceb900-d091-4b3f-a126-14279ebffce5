@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: oklch(1 0 0); /* #ffffff */
  --foreground: oklch(0.35 0.01 258); /* #475569 */
  --card: oklch(0.97 0.005 258); /* #f1f5f9 */
  --card-foreground: oklch(0.35 0.01 258); /* #475569 */
  --popover: oklch(1 0 0); /* #ffffff */
  --popover-foreground: oklch(0.35 0.01 258); /* #475569 */
  --primary: oklch(0.45 0.15 165); /* #059669 emerald-600 */
  --primary-foreground: oklch(1 0 0); /* #ffffff */
  --secondary: oklch(0.97 0.005 258); /* #f1f5f9 */
  --secondary-foreground: oklch(0.35 0.01 258); /* #475569 */
  --muted: oklch(0.98 0.003 258); /* #f8fafc */
  --muted-foreground: oklch(0.35 0.01 258); /* #475569 */
  --accent: oklch(0.55 0.15 165); /* #10b981 emerald-500 */
  --accent-foreground: oklch(1 0 0); /* #ffffff */
  --destructive: oklch(0.55 0.22 35); /* #ea580c */
  --destructive-foreground: oklch(1 0 0); /* #ffffff */
  --border: oklch(0.85 0.005 258); /* #d1d5db */
  --input: oklch(1 0 0); /* #ffffff */
  --ring: oklch(0.45 0.15 165 / 0.2); /* primary with opacity */
  --chart-1: oklch(0.45 0.15 165); /* #059669 */
  --chart-2: oklch(0.55 0.15 165); /* #10b981 */
  --chart-3: oklch(0.32 0.01 258); /* #4b5563 */
  --chart-4: oklch(0.55 0.25 270); /* #6366f1 */
  --chart-5: oklch(0.55 0.22 35); /* #ea580c */
  --radius: 0.5rem;
  --sidebar: oklch(1 0 0); /* #ffffff */
  --sidebar-foreground: oklch(0.35 0.01 258); /* #475569 */
  --sidebar-primary: oklch(0.45 0.15 165); /* #059669 */
  --sidebar-primary-foreground: oklch(1 0 0); /* #ffffff */
  --sidebar-accent: oklch(0.55 0.15 165); /* #10b981 */
  --sidebar-accent-foreground: oklch(1 0 0); /* #ffffff */
  --sidebar-border: oklch(0.85 0.005 258); /* #d1d5db */
  --sidebar-ring: oklch(0.45 0.15 165 / 0.2); /* primary with opacity */
}

.dark {
  --background: oklch(0.12 0.01 258); /* Dark background */
  --foreground: oklch(0.95 0.005 258); /* Light text */
  --card: oklch(0.15 0.01 258); /* Dark card */
  --card-foreground: oklch(0.95 0.005 258); /* Light card text */
  --popover: oklch(0.12 0.01 258); /* Dark popover */
  --popover-foreground: oklch(0.95 0.005 258); /* Light popover text */
  --primary: oklch(0.55 0.15 165); /* Brighter primary for dark mode */
  --primary-foreground: oklch(0.12 0.01 258); /* Dark text on primary */
  --secondary: oklch(0.18 0.01 258); /* Dark secondary */
  --secondary-foreground: oklch(0.95 0.005 258); /* Light secondary text */
  --muted: oklch(0.18 0.01 258); /* Dark muted */
  --muted-foreground: oklch(0.65 0.005 258); /* Muted text */
  --accent: oklch(0.6 0.15 165); /* Bright accent */
  --accent-foreground: oklch(0.12 0.01 258); /* Dark text on accent */
  --destructive: oklch(0.6 0.22 35); /* Bright destructive */
  --destructive-foreground: oklch(0.95 0.005 258); /* Light text on destructive */
  --border: oklch(0.25 0.01 258); /* Dark border */
  --input: oklch(0.18 0.01 258); /* Dark input */
  --ring: oklch(0.55 0.15 165 / 0.3); /* Primary ring with opacity */
  --sidebar: oklch(0.1 0.01 258); /* Darker sidebar */
  --sidebar-foreground: oklch(0.95 0.005 258); /* Light sidebar text */
  --sidebar-primary: oklch(0.55 0.15 165); /* Bright sidebar primary */
  --sidebar-primary-foreground: oklch(0.12 0.01 258); /* Dark text on sidebar primary */
  --sidebar-accent: oklch(0.6 0.15 165); /* Bright sidebar accent */
  --sidebar-accent-foreground: oklch(0.12 0.01 258); /* Dark text on sidebar accent */
  --sidebar-border: oklch(0.25 0.01 258); /* Dark sidebar border */
  --sidebar-ring: oklch(0.55 0.15 165 / 0.3); /* Sidebar ring with opacity */
}

@theme inline {
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}

import {getThemeIndex, getModel} from '@/src/lib/content';
import {MarkdownRenderer} from '@/src/components/MarkdownRenderer';
import {Breadcrumbs} from '@/src/components/Breadcrumbs';
import {Button} from '@/components/ui/button';
import {Card, CardContent, CardDescription, CardHeader, CardTitle} from '@/components/ui/card';
import {Badge} from '@/components/ui/badge';
import Link from 'next/link';
import {notFound} from 'next/navigation';
import {ArrowRight, BarChart3} from 'lucide-react';

type ThemePageProps = {
  params: {theme: string};
};

export default function ThemePage({params}: ThemePageProps) {
  try {
    const theme = getThemeIndex(params.theme);

    return (
      <div className="space-y-8">
        <Breadcrumbs items={[{label: 'Home', href: '/'}, {label: theme.title}]} />

        <div className="space-y-6">
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-balance">{theme.title}</h1>
            {theme.descriptionMd && (
              <div className="max-w-3xl mx-auto">
                <MarkdownRenderer content={theme.descriptionMd} />
              </div>
            )}
          </div>

          <div className="flex justify-center">
            <Badge variant="secondary" className="text-sm">
              {theme.models.length}
              {' '}
              {theme.models.length === 1 ? 'Model' : 'Models'}
              {' '}
              Available
            </Badge>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {theme.models.map((modelRef) => {
            try {
              const model = getModel(params.theme, modelRef.modelId);
              return (
                <Card
                  key={model.modelId}
                  className="group hover:shadow-lg transition-all duration-200 hover:border-primary/50"
                >
                  <CardHeader>
                    <div className="flex items-start justify-between">
                      <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center mb-3">
                        <BarChart3 className="h-5 w-5 text-primary" />
                      </div>
                      {model.tags && (
                        <Badge variant="outline" className="text-xs">
                          {model.dimensions.length}
                          {' '}
                          dimensions
                        </Badge>
                      )}
                    </div>
                    <CardTitle className="text-xl text-balance">{model.title}</CardTitle>
                    <CardDescription className="text-pretty">
                      {model.shortDescription || 'Assess your maturity across key dimensions'}
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      {model.tags && (
                        <div className="flex flex-wrap gap-1">
                          {model.tags.slice(0, 3).map((tag) => (
                            <Badge key={tag} variant="secondary" className="text-xs">
                              {tag}
                            </Badge>
                          ))}
                        </div>
                      )}
                      <Button
                        asChild
                        className="w-full group-hover:bg-primary group-hover:text-primary-foreground transition-colors bg-transparent"
                        variant="outline"
                      >
                        <Link href={`/t/${params.theme}/${model.modelId}`}>
                          Assess
                          {' '}
                          {model.title}
                          {' '}
                          <ArrowRight className="ml-2 h-4 w-4" />
                        </Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              );
            } catch {
              // Fallback for models that can't be loaded
              return (
                <Card key={modelRef.modelId} className="opacity-60">
                  <CardHeader>
                    <CardTitle className="text-xl text-balance">{modelRef.title}</CardTitle>
                    <CardDescription>Model temporarily unavailable</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <Button variant="outline" disabled className="w-full bg-transparent">
                      Coming Soon
                    </Button>
                  </CardContent>
                </Card>
              );
            }
          })}
        </div>

        <div className="text-center pt-8">
          <div className="space-y-4">
            <h2 className="text-2xl font-semibold text-balance">Ready to Get Started?</h2>
            <p className="text-muted-foreground max-w-2xl mx-auto text-balance">
              Choose a model above to begin your maturity
              assessment and get clear guidance on improvement areas.
            </p>
          </div>
        </div>
      </div>
    );
  } catch {
    notFound();
  }
}

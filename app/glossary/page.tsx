import {Breadcrumbs} from '@/src/components/Breadcrumbs';
import {<PERSON><PERSON><PERSON>ender<PERSON>} from '@/src/components/MarkdownRenderer';

const glossaryContent = `
# Glossary

## Key Terms and Concepts

### Maturity Model
A framework that describes the evolution of capabilities from ad-hoc practices to optimized, continuously improving processes.

### Dimension
A specific area of capability being assessed within a maturity model (e.g., "Architecture Principles", "Technical Debt Management").

### Maturity Level
A stage in the evolution of a capability, typically ranging from 0 (Absent) to 5 (Optimizing).

### Assessment
The process of evaluating an organization's current maturity level across different dimensions.

### Gap Analysis
The identification of differences between current maturity levels and desired target levels.

## Architecture Terms

### Technical Debt
The implied cost of additional rework caused by choosing an easy solution now instead of using a better approach that would take longer.

### Architecture Principles
Fundamental rules and guidelines that inform and support the way an organization fulfills its mission through IT systems.

### Design Review
A systematic examination of a design to evaluate its adequacy and identify problems before implementation.

## DevOps Terms

### Infrastructure as Code (IaC)
The practice of managing and provisioning computing infrastructure through machine-readable definition files.

### CI/CD
Continuous Integration and Continuous Deployment - practices that enable frequent, reliable software releases.

### Platform Engineering
The discipline of designing and building toolchains and workflows that enable self-service capabilities for software engineering organizations.

## Process Terms

### Standard Operating Procedure (SOP)
A set of step-by-step instructions compiled by an organization to help workers carry out routine operations.

### Key Performance Indicator (KPI)
A measurable value that demonstrates how effectively a company is achieving key business objectives.

### Service Level Agreement (SLA)
A commitment between a service provider and a client regarding the expected level of service.

## Quality Terms

### Test Coverage
A measure of how much of the source code is executed when a particular test suite runs.

### Code Quality
A measure of how well-written, maintainable, and reliable source code is.

### Automated Testing
The practice of running tests automatically as part of the software development process.

## Organizational Terms

### Cross-functional Team
A group of people with different functional expertise working toward a common goal.

### DevSecOps
A set of practices that integrates security into the DevOps process.

### Continuous Improvement
An ongoing effort to improve products, services, or processes through incremental and breakthrough improvements.
`;

export default function GlossaryPage() {
  return (
    <div className="space-y-6">
      <Breadcrumbs items={[{label: 'Home', href: '/'}, {label: 'Glossary'}]} />

      <div className="max-w-4xl">
        <MarkdownRenderer content={glossaryContent} />
      </div>
    </div>
  );
}

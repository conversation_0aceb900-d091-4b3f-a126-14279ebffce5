import type React from 'react';
import type {Metadata} from 'next';
import {GeistSans} from 'geist/font/sans';
import {GeistMono} from 'geist/font/mono';
import {Analytics} from '@vercel/analytics/next';
import './globals.css';
import {getAllThemes, buildSearchIndex} from '@/src/lib/content';
import {SidebarNav} from '@/src/components/SidebarNav';
import {Search} from '@/src/components/Search';
import {ThemeToggle} from '@/src/components/ThemeToggle';
import {Suspense} from 'react';
import {SpeedInsights} from '@vercel/speed-insights/next';

export const metadata: Metadata = {
  title: 'Software Maturity Models | Assess & Improve',
  description: 'Clear maturity models for software, delivery, and org practices. Assess your team and plan next steps.',
  generator: 'v0.app',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const themes = getAllThemes();
  const searchIndex = buildSearchIndex();

  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`font-sans ${GeistSans.variable} ${GeistMono.variable} min-h-screen bg-background text-foreground`}
      >
        <div className="flex min-h-screen">
          <div className="hidden [&:has(~_div_main_div:first-child:not([data-has-sidebar]))]:block">
            <SidebarNav themes={themes} />
          </div>

          <div className="flex-1 min-w-0">
            <div className="[main_div:first-child:not([data-has-sidebar])_&]:hidden">
              <header className="sticky top-0 z-30 border-b border-border bg-background/80 backdrop-blur">
                <div className="mx-auto max-w-6xl px-4 py-3 flex items-center justify-between">
                  <div></div>

                  <div className="flex-1 max-w-md mx-4">
                    <Suspense fallback={<div>Loading...</div>}>
                      <Search searchIndex={searchIndex} />
                    </Suspense>
                  </div>

                  <div className="flex items-center gap-2">
                    <a
                      href="https://github.com/yourusername/software-maturity-models"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center justify-center w-8 h-8 rounded-md hover:bg-muted transition-colors"
                      aria-label="View on GitHub"
                    >
                      <svg
                        width="20"
                        height="20"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M9 19c-5 1.5-5-2.5-7-3m14 6v-3.87a3.37 3.37 0 0 0-.94-2.61c3.14-.35 6.44-1.54 6.44-7A5.44 5.44 0 0 0 20 4.77 5.07 5.07 0 0 0 19.91 1S18.73.65 16 2.48a13.38 13.38 0 0 0-7 0C6.27.65 5.09 1 5.09 1A5.07 5.07 0 0 0 5 4.77a5.44 5.44 0 0 0-1.5 3.78c0 5.42 3.3 6.61 6.44 7A3.37 3.37 0 0 0 9 18.13V22" />
                      </svg>
                    </a>
                    <ThemeToggle />
                  </div>
                </div>
              </header>
            </div>

            <main className="[&:has(div:first-child:not([data-has-sidebar]))]:mx-0 [&:has(div:first-child:not([data-has-sidebar]))]:px-0 [&:has(div:first-child:not([data-has-sidebar]))]:py-0 mx-auto max-w-6xl px-4 py-6">
              {children}
            </main>

            <footer className="mx-auto max-w-6xl px-4 py-8 text-sm text-muted-foreground border-t border-border">
              <div className="space-y-2">
                <div className="text-left">
                  © 2025 Sean. Content
                  {' '}
                  <a
                    href="https://creativecommons.org/licenses/by-sa/4.0/"
                    className="underline hover:text-foreground"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    CC BY-SA 4.0
                  </a>
                  . Code
                  {' '}
                  <a
                    href="https://www.apache.org/licenses/LICENSE-2.0"
                    className="underline hover:text-foreground"
                    target="_blank"
                    rel="noopener noreferrer"
                  >
                    Apache-2.0
                  </a>
                  .
                  {' '}
                  <a href="/license" className="underline hover:text-foreground">
                    Full license details
                  </a>
                  .
                </div>
                <div className="text-xs text-muted-foreground/80">
                  Brand assets not covered by CC; all rights reserved.
                </div>
              </div>
            </footer>
          </div>
        </div>
        <Analytics />
        <SpeedInsights />
      </body>
    </html>
  );
}

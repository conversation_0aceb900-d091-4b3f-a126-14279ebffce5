import {Breadcrumbs} from '@/src/components/Breadcrumbs';
import {<PERSON><PERSON><PERSON>enderer} from '@/src/components/MarkdownRenderer';

const aboutContent = `
# About Software Maturity Models

## Our Mission

We believe that every software organization deserves clarity on where they stand and a clear path to improvement. Our comprehensive maturity models provide that clarity through evidence-based assessment frameworks and actionable guidance.

## Why Maturity Models Matter

Software development is complex, and organizations often struggle to understand:
- Where they currently stand in their capabilities
- What "good" looks like in their context
- How to prioritize improvement efforts
- Whether their investments are paying off

Maturity models address these challenges by providing:
- **Objective Assessment** - Clear criteria for evaluating current capabilities
- **Benchmarking** - Understanding relative to industry standards
- **Roadmaps** - Step-by-step guidance for improvement
- **Progress Tracking** - Measurable indicators of advancement

## Our Approach

### Evidence-Based
Our models are grounded in research, industry best practices, and real-world validation. Each level is defined by observable, measurable characteristics.

### Practical
We focus on actionable guidance rather than theoretical frameworks. Every assessment includes specific next steps and improvement recommendations.

### Comprehensive
Our models cover the full spectrum of software development capabilities, from technical practices to organizational processes.

### Continuously Improved
We regularly update our models based on industry evolution, user feedback, and new research.

## Model Coverage

Our current models span:

- **Architecture** - Foundational practices, API lifecycle, system design
- **DevOps & Platform** - Infrastructure, deployment, developer experience
- **Code Quality & Testing** - Testing strategies, code standards, quality gates
- **Security & Compliance** - Security practices, compliance frameworks
- **Agile & Delivery** - Development processes, team practices
- **Observability** - Monitoring, logging, incident response

## How to Use This Site

1. **Start with Assessment** - Choose a relevant model and assess your current state
2. **Identify Gaps** - Compare your current level with your target maturity
3. **Plan Improvements** - Use our guidance to create an improvement roadmap
4. **Track Progress** - Regular re-assessment to measure advancement
5. **Share Results** - Use export features to communicate with stakeholders

## Open Source

This project is open source and welcomes contributions from the community. You can:
- Suggest improvements to existing models
- Propose new models or dimensions
- Report issues or inconsistencies
- Share your assessment experiences

## Contact

For questions, suggestions, or collaboration opportunities, please reach out through our GitHub repository or community forums.

---

*Built with ❤️ for the software development community*
`;

export default function AboutPage() {
  return (
    <div className="space-y-6">
      <Breadcrumbs items={[{label: 'Home', href: '/'}, {label: 'About'}]} />

      <div className="max-w-4xl">
        <MarkdownRenderer content={aboutContent} />
      </div>
    </div>
  );
}

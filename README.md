# Software Maturity Models

Comprehensive maturity models for software development, delivery, and organizational practices. Assess your team's current capabilities and plan strategic improvements across multiple domains.

[![Deployed on Vercel](https://img.shields.io/badge/Deployed%20on-Vercel-black?style=for-the-badge&logo=vercel)](https://v0-software-maturity-models.vercel.app/)
[![Built with v0](https://img.shields.io/badge/Built%20with-v0.app-black?style=for-the-badge)](https://v0.app/chat/projects/kxd0FJFj7WM)

## Overview

This repository provides a comprehensive collection of software maturity models covering architecture, DevOps, quality assurance, security, and agile practices. Each model offers structured assessment frameworks to help organizations understand their current capabilities and plan strategic improvements.

## Features

- **Multi-Domain Coverage**: Architecture, DevOps, Quality, Security, Agile practices
- **Structured Assessment**: 6-level maturity scale (0-5) based on industry standards
- **Interactive Interface**: Web-based tool for easy navigation and assessment
- **Export Capabilities**: Download assessments in multiple formats
- **Search Functionality**: Find specific practices and guidance quickly

## Getting Started

Visit the live application at: **[https://v0-software-maturity-models.vercel.app/](https://v0-software-maturity-models.vercel.app/)**

### Local Development

```bash
# Clone the repository
git clone https://github.com/seanblonien/software-maturity-models.git
cd software-maturity-models

# Install dependencies
pnpm install

# Start development server
pnpm dev
```

## Licensing

This project uses a dual-license structure to appropriately cover different types of content:

- **Code**: [Apache License 2.0](LICENSE) - All source code, configuration files, and technical implementation
- **Content**: [Creative Commons Attribution-ShareAlike 4.0 International](LICENSE-CONTENT) - All maturity models, documentation, and educational content

**Contributions are accepted under the same licenses.** By submitting a pull request, you agree that your code contributions will be licensed under Apache-2.0 and your content contributions will be licensed under CC BY-SA 4.0.

### How to Attribute

When using or referencing the maturity models content:

```
Software Maturity Models by Sean Blonien is licensed under CC BY-SA 4.0.
Available at: https://github.com/seanblonien/software-maturity-models
```

For code reuse, follow standard Apache-2.0 attribution requirements.

**License Links:**
- [Apache License 2.0 Full Text](https://www.apache.org/licenses/LICENSE-2.0)
- [CC BY-SA 4.0 Full Text](https://creativecommons.org/licenses/by-sa/4.0/)

**Note**: Brand assets (logos, wordmarks) are not covered by the Creative Commons license and all rights are reserved.
